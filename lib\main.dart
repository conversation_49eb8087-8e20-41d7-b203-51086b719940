import 'package:flutter/material.dart';
import 'package:newscloud/services/news_service.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(),

        body: Center(
          child: GestureDetector(
            onTap: () async {
              NewsApi newsApi = NewsApi();
              var articles = await newsApi.fetchArticles();
              for (var article in articles) {
                print(article.title);
              }
            },
            child: Container(
              decoration: BoxDecoration(),
              child: Text("fetch Data "),
            ),
          ),
        ),
      ),
    );
  }
}
