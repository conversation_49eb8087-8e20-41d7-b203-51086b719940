import 'dart:convert';
import 'package:newscloud/models/article_model.dart';
import 'package:newscloud/models/articles_model.dart';

import 'package:http/http.dart' as http;

class NewsApi {
  final String apiKey = '********************************';

  Future<List<dynamic>> fetchArticles() async {
    try {
      final url = Uri.parse(
        'https://newsapi.org/v2/top-headlines?country=us&category=sports&apiKey=$apiKey',
      );
      http.Response response = await http.get(url);

      if (response.statusCode == 200) {
        String data =response.body;
       var jsonData = jsonDecode(data);
        Articles articles = Articles.fromJson(jsonData);
        List<Article> articlesList =
            articles.articles.map((e)=>Article.fromJson(e)).toList();
        return articlesList;
      } else {
        print('status code : ${response.statusCode}');
      }
    } catch (ex) {
      print('Error fetching articles: $ex');
      // return []; // Return empty list instead of null
     }
  }
}
